#!/usr/bin/env python3
"""
VisionFrame AI Video Processor
Processes JSON templates to create videos similar to Creatomate output
"""

import json
import os
import sys
from typing import Dict, List, Any, Tuple
import logging
from pathlib import Path

# Video processing imports
try:
    # Try MoviePy 1.x imports first (most common)
    try:
        from moviepy.editor import VideoFileClip, ImageClip, AudioFileClip, ColorClip, CompositeVideoClip, concatenate_videoclips
        print("✅ Using MoviePy 1.x imports")
    except ImportError as e1:
        print(f"MoviePy 1.x import failed: {e1}")
        # Try MoviePy 2.x specific imports
        try:
            from moviepy.video.io.VideoFileClip import VideoFileClip
            from moviepy.video.VideoClip import ImageClip, ColorClip
            from moviepy.audio.io.AudioFileClip import AudioFileClip
            from moviepy.video.compositing.CompositeVideoClip import CompositeVideoClip
            from moviepy.video.compositing.concatenate import concatenate_videoclips
            print("✅ Using MoviePy 2.x specific imports")
        except ImportError as e2:
            print(f"MoviePy 2.x specific import failed: {e2}")
            # Try alternative MoviePy 2.x imports
            try:
                import moviepy.video.io.VideoFileClip as vfc
                import moviepy.video.VideoClip as vc
                import moviepy.audio.io.AudioFileClip as afc
                import moviepy.video.compositing.CompositeVideoClip as cvc
                import moviepy.video.compositing.concatenate as concat

                VideoFileClip = vfc.VideoFileClip
                ImageClip = vc.ImageClip
                ColorClip = vc.ColorClip
                AudioFileClip = afc.AudioFileClip
                CompositeVideoClip = cvc.CompositeVideoClip
                concatenate_videoclips = concat.concatenate_videoclips
                print("✅ Using MoviePy 2.x alternative imports")
            except ImportError as e3:
                print(f"All MoviePy import methods failed:")
                print(f"  Method 1: {e1}")
                print(f"  Method 2: {e2}")
                print(f"  Method 3: {e3}")
                raise ImportError("Cannot import MoviePy classes. Please check your MoviePy installation.")

    import numpy as np
    from PIL import Image, ImageDraw, ImageFont
    import requests
    from io import BytesIO
except ImportError as e:
    print(f"Missing required dependencies: {e}")
    print("Install with: pip install moviepy pillow requests numpy")
    sys.exit(1)

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class VideoProcessor:
    """Main video processor class that handles JSON template processing"""

    def __init__(self, output_dir: str = "output"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.temp_dir = Path("temp")
        self.temp_dir.mkdir(exist_ok=True)

        # Default settings
        self.default_font_path = self.get_default_font()
        self.fps = 30

    def get_default_font(self) -> str:
        """Get default font path for the system"""
        try:
            # Try to find Montserrat or similar fonts
            font_paths = [
                "/System/Library/Fonts/Helvetica.ttc",  # macOS
                "C:/Windows/Fonts/arial.ttf",  # Windows
                "/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf",  # Linux
                "/usr/share/fonts/TTF/arial.ttf"  # Linux alternative
            ]

            for font_path in font_paths:
                if os.path.exists(font_path):
                    return font_path

            # Fallback to PIL default
            return None
        except Exception:
            return None

    def process_template(self, template_data: Dict[str, Any], output_filename: str = None) -> str:
        """Process a complete video template"""
        logger.info("Starting video processing...")

        # Extract template properties
        width = template_data.get('width', 720)
        height = template_data.get('height', 1280)
        output_format = template_data.get('output_format', 'mp4')

        if not output_filename:
            output_filename = f"output_video.{output_format}"

        output_path = self.output_dir / output_filename

        # Process all scenes
        scene_clips = []
        total_duration = 0

        for element in template_data.get('elements', []):
            if element.get('type') == 'composition':
                scene_clip = self.process_scene(element, width, height)
                if scene_clip:
                    scene_clips.append(scene_clip)
                    total_duration += scene_clip.duration
                    logger.info(f"Processed scene: {element.get('name', 'Unknown')} - Duration: {scene_clip.duration:.2f}s")

        if not scene_clips:
            raise ValueError("No valid scenes found in template")

        # Concatenate all scenes
        logger.info("Concatenating scenes...")
        final_video = concatenate_videoclips(scene_clips)

        # Write final video
        logger.info(f"Writing final video to {output_path}...")
        final_video.write_videofile(
            str(output_path),
            fps=self.fps,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True
        )

        # Cleanup
        final_video.close()
        for clip in scene_clips:
            clip.close()

        logger.info(f"Video processing complete! Output: {output_path}")
        return str(output_path)

    def process_scene(self, scene_data: Dict[str, Any], width: int, height: int) -> VideoFileClip:
        """Process a single scene composition"""
        scene_name = scene_data.get('name', 'Unknown Scene')
        logger.info(f"Processing scene: {scene_name}")

        elements = scene_data.get('elements', [])
        if not elements:
            return None

        # Separate elements by type
        image_elements = [e for e in elements if e.get('type') == 'image']
        text_elements = [e for e in elements if e.get('type') == 'text']
        audio_elements = [e for e in elements if e.get('type') == 'audio']

        # Determine scene duration from audio or default
        scene_duration = 3.0  # Default duration
        audio_clip = None

        if audio_elements:
            # Load the first audio element
            audio_element = audio_elements[0]
            audio_source = audio_element.get('source', '')

            if audio_source and os.path.exists(audio_source):
                try:
                    from moviepy.editor import AudioFileClip
                    audio_clip = AudioFileClip(audio_source)
                    scene_duration = audio_clip.duration
                    logger.info(f"Loaded audio: {audio_source}, duration: {scene_duration:.2f}s")
                    logger.info(f"Audio clip created successfully: {type(audio_clip)}")
                except Exception as e:
                    logger.warning(f"Failed to load audio {audio_source}: {e}")
                    audio_clip = None
                    scene_duration = audio_element.get('duration', 5.0)
            else:
                logger.warning(f"Audio source not found: {audio_source}")
                scene_duration = audio_element.get('duration', 5.0)

        # Create background video from images
        background_clip = self.create_background_clip(image_elements, width, height, scene_duration)

        # Create text overlays
        text_clips = []
        for text_element in text_elements:
            # Find linked audio element for transcript
            transcript_source_id = text_element.get('transcript_source')
            transcript_text = None

            if transcript_source_id:
                # Find corresponding audio element
                audio_element = next((e for e in audio_elements if e.get('id') == transcript_source_id), None)
                if audio_element and 'transcript' in audio_element:
                    transcript_text = audio_element['transcript']
                    logger.info(f"Using transcript from audio {transcript_source_id}: {transcript_text[:50]}...")

            text_clip = self.create_text_clip(text_element, width, height, scene_duration, transcript_text)
            if text_clip:
                text_clips.append(text_clip)

        # Composite all elements
        video_clips = [background_clip] + text_clips

        if audio_clip:
            logger.info(f"Attaching audio to scene: {type(audio_clip)}, duration: {audio_clip.duration:.2f}s")
            final_clip = CompositeVideoClip(video_clips).set_audio(audio_clip)
            logger.info("Audio successfully attached to video clip")
        else:
            logger.warning("No audio clip found - creating video without audio")
            final_clip = CompositeVideoClip(video_clips)

        return final_clip.set_duration(scene_duration)

    def create_background_clip(self, image_elements: List[Dict], width: int, height: int, duration: float) -> VideoFileClip:
        """Create background video clip with image and animations"""
        logger.info(f"Creating background clip with {len(image_elements)} image elements")

        if not image_elements:
            # Create solid color background
            return ColorClip(size=(width, height), color=(0, 0, 0), duration=duration)

        image_element = image_elements[0]  # Use first image
        logger.info(f"Using image element: {image_element}")

        # Try to load actual image or create placeholder
        image_source = image_element.get('source', '')
        image_clip = self.load_image_clip(image_source, width, height, duration)
        logger.info(f"Loaded image clip, type: {type(image_clip)}, size: {getattr(image_clip, 'size', 'No size')}")

        # Apply animations
        animations = image_element.get('animations', [])
        logger.info(f"Applying {len(animations)} animations")
        for animation in animations:
            if animation.get('type') == 'pan':
                logger.info(f"Applying pan animation: {animation}")
                image_clip = self.apply_pan_animation(image_clip, animation, width, height)
                logger.info(f"After pan animation, clip type: {type(image_clip)}, size: {getattr(image_clip, 'size', 'No size')}")

        # Apply color overlay
        color_overlay = image_element.get('color_overlay', 'rgba(0,0,0,0.15)')
        if color_overlay and color_overlay != 'rgba(0,0,0,0)':
            try:
                logger.info(f"Applying color overlay: {color_overlay}")
                overlay_color = self.parse_rgba_color(color_overlay)
                logger.info(f"Parsed overlay color: {overlay_color}")
                overlay_clip = ColorClip(size=(width, height), color=overlay_color[:3], duration=duration)
                overlay_clip = overlay_clip.set_opacity(overlay_color[3])
                logger.info(f"Created overlay clip, type: {type(overlay_clip)}")
                logger.info(f"Before composite - image_clip type: {type(image_clip)}, size: {getattr(image_clip, 'size', 'No size')}")
                image_clip = CompositeVideoClip([image_clip, overlay_clip])
                logger.info(f"After composite - image_clip type: {type(image_clip)}, size: {getattr(image_clip, 'size', 'No size')}")
            except Exception as e:
                logger.error(f"Error applying color overlay: {e}")
                raise

        # Resize to final dimensions
        try:
            logger.info(f"Resizing clip to ({width}, {height}), current size: {getattr(image_clip, 'size', 'No size')}")
            final_clip = image_clip.resize(newsize=(width, height))
            logger.info(f"Resize successful, final clip type: {type(final_clip)}")
            return final_clip
        except Exception as e:
            logger.error(f"Error during resize: {e}")
            logger.error(f"Clip type: {type(image_clip)}, size: {getattr(image_clip, 'size', 'No size')}")
            raise

    def load_image_clip(self, source: str, width: int, height: int, duration: float) -> VideoFileClip:
        """Load image from various sources (file, URL, or create placeholder)"""
        try:
            logger.info(f"Loading image clip from source: {source}")
            if source.startswith('http'):
                # Download image from URL
                response = requests.get(source)
                image = Image.open(BytesIO(response.content))
                image_array = np.array(image)
                clip = ImageClip(image_array, duration=duration)
                logger.info(f"Created ImageClip from URL, type: {type(clip)}, size: {getattr(clip, 'size', 'No size')}")
                return clip
            elif os.path.exists(source):
                # Load from local file
                clip = ImageClip(source, duration=duration)
                logger.info(f"Created ImageClip from file, type: {type(clip)}, size: {getattr(clip, 'size', 'No size')}")
                return clip
            else:
                # Create placeholder with source ID as text
                placeholder_text = f"Image: {source[:8]}..." if len(source) > 8 else f"Image: {source}"
                placeholder_image = self.create_placeholder_image(width, height, placeholder_text)
                clip = ImageClip(placeholder_image, duration=duration)
                logger.info(f"Created ImageClip from placeholder, type: {type(clip)}, size: {getattr(clip, 'size', 'No size')}")
                return clip
        except Exception as e:
            logger.warning(f"Failed to load image {source}: {e}. Using placeholder.")
            placeholder_image = self.create_placeholder_image(width, height, "Image Not Found")
            clip = ImageClip(placeholder_image, duration=duration)
            logger.info(f"Created ImageClip from error placeholder, type: {type(clip)}, size: {getattr(clip, 'size', 'No size')}")
            return clip

    def create_text_clip(self, text_element: Dict, width: int, height: int, duration: float, transcript_text: str = None) -> VideoFileClip:
        """Create text overlay clip"""
        # Extract text properties
        font_family = text_element.get('font_family', 'Montserrat')
        font_weight = text_element.get('font_weight', '700')
        font_size = text_element.get('font_size', '8 vmin')
        fill_color = text_element.get('fill_color', '#ffffff')
        stroke_color = text_element.get('stroke_color', '#333333')
        stroke_width = text_element.get('stroke_width', '1.05 vmin')

        # Calculate actual font size (vmin = 1% of viewport minimum dimension)
        min_dimension = min(width, height)
        font_size_px = int(min_dimension * 0.8)  # Increased to 80% for extremely large font

        # Ensure minimum font size for readability
        font_size_px = max(font_size_px, 150)  # Minimum 150px font size

        logger.info(f"Video processor calculated font size: {font_size_px}px for dimensions {width}x{height}")

        # Use transcript text if available, otherwise use sample text
        if transcript_text:
            # Use full transcript text without truncation to avoid cutting off subtitles
            display_text = transcript_text
            logger.info(f"Using full transcript text: {display_text}")
        else:
            display_text = "Sample subtitle text\nwith highlighting effect"
            logger.info("Using sample text for subtitle")

        # Create text image
        text_image = self.create_text_image(
            display_text,
            font_size_px,
            fill_color,
            stroke_color,
            width,
            height,
            text_element
        )

        # Create text clip
        text_clip = ImageClip(text_image, duration=duration)

        # Position text
        x_alignment = text_element.get('x_alignment', '50%')
        y_alignment = text_element.get('y_alignment', '50%')

        # Get image dimensions from numpy array shape (height, width, channels)
        text_image_height, text_image_width = text_image.shape[:2]

        x_pos = self.parse_percentage(x_alignment, width) - text_image_width // 2
        y_pos = self.parse_percentage(y_alignment, height) - text_image_height // 2

        text_clip = text_clip.set_position((x_pos, y_pos))

        # Apply transcript highlighting effect
        transcript_effect = text_element.get('transcript_effect', 'highlight')
        if transcript_effect == 'highlight' and transcript_text:
            # Use TextProcessor for word-by-word highlighting
            try:
                from text_processor import TextProcessor
                text_processor = TextProcessor()
                highlighted_clip = text_processor.create_highlighted_text_clip(
                    text_element, width, height, duration, display_text
                )
                if highlighted_clip:
                    logger.info("Applied word-by-word highlighting effect")
                    return highlighted_clip
            except Exception as e:
                logger.warning(f"Failed to apply highlighting effect: {e}")

        return text_clip

    def create_placeholder_image(self, width: int, height: int, text: str) -> np.ndarray:
        """Create a placeholder image with text"""
        # Create PIL image
        img = Image.new('RGB', (width, height), color=(100, 100, 100))
        draw = ImageDraw.Draw(img)

        # Try to load font
        try:
            font = ImageFont.truetype(self.default_font_path, 40) if self.default_font_path else ImageFont.load_default()
        except:
            font = ImageFont.load_default()

        # Calculate text position
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]

        x = (width - text_width) // 2
        y = (height - text_height) // 2

        # Draw text
        draw.text((x, y), text, fill=(255, 255, 255), font=font)

        # Convert to numpy array
        return np.array(img)

    def create_text_image(self, text: str, font_size: int, fill_color: str, stroke_color: str,
                         canvas_width: int, canvas_height: int, text_element: Dict) -> np.ndarray:
        """Create text image with styling"""
        # Parse colors
        fill_rgb = self.hex_to_rgb(fill_color)
        stroke_rgb = self.hex_to_rgb(stroke_color)

        # Calculate text dimensions
        text_width = int(canvas_width * 0.8)  # 80% of canvas width
        text_height = int(canvas_height * 0.3)  # 30% of canvas height

        # Create image
        img = Image.new('RGBA', (text_width, text_height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)

        # Load font
        try:
            font = ImageFont.truetype(self.default_font_path, font_size) if self.default_font_path else ImageFont.load_default()
        except:
            font = ImageFont.load_default()

        # Calculate text position for center alignment
        bbox = draw.textbbox((0, 0), text, font=font)
        text_w = bbox[2] - bbox[0]
        text_h = bbox[3] - bbox[1]

        x = (text_width - text_w) // 2
        y = (text_height - text_h) // 2

        # Draw text with stroke
        stroke_width = 2
        for adj_x in range(-stroke_width, stroke_width + 1):
            for adj_y in range(-stroke_width, stroke_width + 1):
                if adj_x != 0 or adj_y != 0:
                    draw.text((x + adj_x, y + adj_y), text, fill=stroke_rgb, font=font)

        # Draw main text
        draw.text((x, y), text, fill=fill_rgb, font=font)

        return np.array(img)

    def apply_pan_animation(self, clip: VideoFileClip, animation: Dict, width: int, height: int) -> VideoFileClip:
        """Apply pan/zoom animation to clip"""
        try:
            start_scale = self.parse_percentage(animation.get('start_scale', '100%'), 100) / 100
            end_scale = self.parse_percentage(animation.get('end_scale', '100%'), 100) / 100

            def scale_function(t):
                progress = t / clip.duration if clip.duration > 0 else 0
                current_scale = start_scale + (end_scale - start_scale) * progress
                # Return tuple (width, height) scaled by current_scale
                return (int(clip.w * current_scale), int(clip.h * current_scale))

            return clip.resize(scale_function)
        except Exception as e:
            logger.error(f"Error in apply_pan_animation: {e}")
            logger.error(f"Clip type: {type(clip)}, Clip size: {getattr(clip, 'size', 'No size attr')}")
            logger.error(f"Animation: {animation}")
            raise

    def apply_highlight_effect(self, text_clip: VideoFileClip, text_element: Dict) -> VideoFileClip:
        """Apply highlighting effect to text"""
        # For now, return the clip as-is
        # In a full implementation, you'd create word-by-word highlighting
        return text_clip

    def parse_percentage(self, value: str, reference: int) -> int:
        """Parse percentage string to pixel value"""
        if isinstance(value, str) and value.endswith('%'):
            percentage = float(value[:-1])
            return int(reference * percentage / 100)
        return int(value)

    def parse_rgba_color(self, rgba_str: str) -> Tuple[int, int, int, float]:
        """Parse RGBA color string"""
        # Extract values from rgba(r,g,b,a) format
        rgba_str = rgba_str.replace('rgba(', '').replace(')', '')
        values = [v.strip() for v in rgba_str.split(',')]

        r, g, b = int(values[0]), int(values[1]), int(values[2])
        a = float(values[3])

        return (r, g, b, a)

    def hex_to_rgb(self, hex_color: str) -> Tuple[int, int, int]:
        """Convert hex color to RGB tuple"""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))

def main():
    """Main function for command line usage"""
    if len(sys.argv) < 2:
        print("Usage: python video_processor.py <template.json> [output_filename]")
        sys.exit(1)

    template_file = sys.argv[1]
    output_filename = sys.argv[2] if len(sys.argv) > 2 else None

    # Load template
    with open(template_file, 'r') as f:
        template_data = json.load(f)

    # Process video
    processor = VideoProcessor()
    output_path = processor.process_template(template_data, output_filename)

    print(f"Video created successfully: {output_path}")

if __name__ == "__main__":
    main()
