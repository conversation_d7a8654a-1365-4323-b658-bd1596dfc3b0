#!/usr/bin/env python3
"""
Advanced Text Processing for Video Generation
Handles transcript highlighting, word-by-word animations, and text styling
"""

import numpy as np
from PIL import Image, ImageDraw, ImageFont
from typing import Dict, List, Tuple, Any
import re
import logging

# Flexible MoviePy imports
try:
    from moviepy.editor import Video<PERSON>ile<PERSON>lip, ImageClip, CompositeVideoClip
except ImportError:
    try:
        from moviepy.video.io.VideoFileClip import VideoFileClip
        from moviepy.video.VideoClip import ImageClip
        from moviepy.video.compositing.CompositeVideoClip import CompositeVideoClip
    except ImportError:
        from moviepy import VideoFileClip, ImageClip, CompositeVideoClip

logger = logging.getLogger(__name__)

class TextProcessor:
    """Advanced text processor for video generation"""

    def __init__(self, default_font_path: str = None):
        self.default_font_path = default_font_path

    def create_highlighted_text_clip(self, text_element: Dict, width: int, height: int,
                                   duration: float, transcript_text: str = None) -> VideoFileClip:
        """Create text clip with word-by-word highlighting effect"""

        # Extract text properties
        font_family = text_element.get('font_family', 'Montserrat')
        font_size = text_element.get('font_size', '8 vmin')
        fill_color = text_element.get('fill_color', '#ffffff')
        stroke_color = text_element.get('stroke_color', '#333333')
        transcript_color = text_element.get('transcript_color', '#ff0040')

        # Calculate font size - make it much larger for better visibility
        min_dimension = min(width, height)
        font_size_px = int(min_dimension * 0.4)  # Increased to 40% for maximum visibility

        # Use provided transcript or sample text
        if not transcript_text:
            transcript_text = "This is a sample transcript text that will be highlighted word by word"

        # Split text into words
        words = transcript_text.split()

        # Calculate timing for each word
        words_per_second = 2.5  # Average speaking rate
        word_duration = 1.0 / words_per_second

        # Create individual word clips
        word_clips = []

        for i, word in enumerate(words):
            start_time = i * word_duration

            if start_time >= duration:
                break

            # Create highlighted version (current word)
            highlighted_img = self.create_word_image(
                words, i, font_size_px, fill_color, stroke_color,
                transcript_color, width, height, text_element
            )

            # Create normal version (other words)
            normal_img = self.create_word_image(
                words, -1, font_size_px, fill_color, stroke_color,
                transcript_color, width, height, text_element
            )

            # Create clip for this word highlight
            word_clip = ImageClip(highlighted_img, duration=word_duration)
            word_clip = word_clip.set_start(start_time)

            word_clips.append(word_clip)

        # Create base text clip (all words normal)
        base_img = self.create_word_image(
            words, -1, font_size_px, fill_color, stroke_color,
            transcript_color, width, height, text_element
        )
        base_clip = ImageClip(base_img, duration=duration)

        # Composite all clips
        all_clips = [base_clip] + word_clips
        final_clip = CompositeVideoClip(all_clips)

        # Position the text
        x_alignment = text_element.get('x_alignment', '50%')
        y_alignment = text_element.get('y_alignment', '50%')

        x_pos = self.parse_percentage(x_alignment, width) - base_img.shape[1] // 2
        y_pos = self.parse_percentage(y_alignment, height) - base_img.shape[0] // 2

        return final_clip.set_position((x_pos, y_pos))

    def create_word_image(self, words: List[str], highlight_index: int, font_size: int,
                         fill_color: str, stroke_color: str, highlight_color: str,
                         canvas_width: int, canvas_height: int, text_element: Dict) -> np.ndarray:
        """Create image with specific word highlighted"""

        # Parse colors
        fill_rgb = self.hex_to_rgb(fill_color)
        stroke_rgb = self.hex_to_rgb(stroke_color)
        highlight_rgb = self.hex_to_rgb(highlight_color)

        # Calculate text area - make it much larger for better visibility
        text_width = int(canvas_width * 0.9)  # Increased from 80% to 90%
        text_height = int(canvas_height * 0.8)  # Increased from 30% to 80% - this was the main issue!

        # Create image
        img = Image.new('RGBA', (text_width, text_height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)

        # Load font
        try:
            font = ImageFont.truetype(self.default_font_path, font_size) if self.default_font_path else ImageFont.load_default()
        except:
            font = ImageFont.load_default()

        # Arrange words in lines
        lines = self.wrap_text_to_lines(words, font, text_width, draw)

        # Calculate total text height
        line_height = font_size + 10
        total_text_height = len(lines) * line_height
        start_y = (text_height - total_text_height) // 2

        # Draw each line
        word_index = 0
        for line_idx, line_words in enumerate(lines):
            line_text = ' '.join(line_words)

            # Calculate line position
            bbox = draw.textbbox((0, 0), line_text, font=font)
            line_width = bbox[2] - bbox[0]
            x = (text_width - line_width) // 2
            y = start_y + line_idx * line_height

            # Draw words in this line
            current_x = x
            for word in line_words:
                # Determine color for this word
                if word_index == highlight_index:
                    word_color = highlight_rgb
                else:
                    word_color = fill_rgb

                # Draw word with stroke
                self.draw_text_with_stroke(draw, (current_x, y), word, font, word_color, stroke_rgb, 2)

                # Move to next word position
                word_bbox = draw.textbbox((0, 0), word + ' ', font=font)
                current_x += word_bbox[2] - word_bbox[0]
                word_index += 1

        return np.array(img)

    def wrap_text_to_lines(self, words: List[str], font: ImageFont, max_width: int, draw: ImageDraw) -> List[List[str]]:
        """Wrap words into lines that fit within max_width"""
        lines = []
        current_line = []

        for word in words:
            test_line = current_line + [word]
            test_text = ' '.join(test_line)

            bbox = draw.textbbox((0, 0), test_text, font=font)
            text_width = bbox[2] - bbox[0]

            if text_width <= max_width:
                current_line.append(word)
            else:
                if current_line:
                    lines.append(current_line)
                current_line = [word]

        if current_line:
            lines.append(current_line)

        return lines

    def draw_text_with_stroke(self, draw: ImageDraw, position: Tuple[int, int], text: str,
                            font: ImageFont, fill_color: Tuple[int, int, int],
                            stroke_color: Tuple[int, int, int], stroke_width: int):
        """Draw text with stroke outline"""
        x, y = position

        # Draw stroke
        for adj_x in range(-stroke_width, stroke_width + 1):
            for adj_y in range(-stroke_width, stroke_width + 1):
                if adj_x != 0 or adj_y != 0:
                    draw.text((x + adj_x, y + adj_y), text, fill=stroke_color, font=font)

        # Draw main text
        draw.text((x, y), text, fill=fill_color, font=font)

    def create_subtitle_background(self, text_element: Dict, text_width: int, text_height: int) -> np.ndarray:
        """Create background for subtitle text"""
        bg_color = text_element.get('background_color', 'rgba(216,216,216,0)')

        if bg_color == 'rgba(216,216,216,0)' or 'rgba(0,0,0,0)' in bg_color:
            # Transparent background
            return None

        # Parse background properties
        x_padding = self.parse_percentage(text_element.get('background_x_padding', '26%'), text_width)
        y_padding = self.parse_percentage(text_element.get('background_y_padding', '7%'), text_height)
        border_radius = self.parse_percentage(text_element.get('background_border_radius', '28%'), min(text_width, text_height))

        # Create background image
        bg_width = text_width + 2 * x_padding
        bg_height = text_height + 2 * y_padding

        bg_img = Image.new('RGBA', (bg_width, bg_height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(bg_img)

        # Parse background color
        bg_rgba = self.parse_rgba_color(bg_color)

        # Draw rounded rectangle
        if border_radius > 0:
            self.draw_rounded_rectangle(draw, (0, 0, bg_width, bg_height), border_radius, bg_rgba)
        else:
            draw.rectangle((0, 0, bg_width, bg_height), fill=bg_rgba)

        return np.array(bg_img)

    def draw_rounded_rectangle(self, draw: ImageDraw, bbox: Tuple[int, int, int, int],
                             radius: int, fill_color: Tuple[int, int, int, int]):
        """Draw a rounded rectangle"""
        x1, y1, x2, y2 = bbox

        # Draw main rectangle
        draw.rectangle((x1 + radius, y1, x2 - radius, y2), fill=fill_color)
        draw.rectangle((x1, y1 + radius, x2, y2 - radius), fill=fill_color)

        # Draw corners
        draw.pieslice((x1, y1, x1 + 2*radius, y1 + 2*radius), 180, 270, fill=fill_color)
        draw.pieslice((x2 - 2*radius, y1, x2, y1 + 2*radius), 270, 360, fill=fill_color)
        draw.pieslice((x1, y2 - 2*radius, x1 + 2*radius, y2), 90, 180, fill=fill_color)
        draw.pieslice((x2 - 2*radius, y2 - 2*radius, x2, y2), 0, 90, fill=fill_color)

    def parse_percentage(self, value: str, reference: int) -> int:
        """Parse percentage string to pixel value"""
        if isinstance(value, str) and value.endswith('%'):
            percentage = float(value[:-1])
            return int(reference * percentage / 100)
        return int(value)

    def parse_rgba_color(self, rgba_str: str) -> Tuple[int, int, int, int]:
        """Parse RGBA color string"""
        rgba_str = rgba_str.replace('rgba(', '').replace(')', '')
        values = [v.strip() for v in rgba_str.split(',')]

        r, g, b = int(values[0]), int(values[1]), int(values[2])
        a = int(float(values[3]) * 255)  # Convert to 0-255 range

        return (r, g, b, a)

    def hex_to_rgb(self, hex_color: str) -> Tuple[int, int, int]:
        """Convert hex color to RGB tuple"""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))

class TranscriptProcessor:
    """Process transcript data for timing and synchronization"""

    def __init__(self):
        self.words_per_second = 2.5  # Average speaking rate

    def generate_word_timings(self, text: str, total_duration: float) -> List[Dict]:
        """Generate timing data for each word in the transcript"""
        words = text.split()

        if not words:
            return []

        # Calculate timing
        word_duration = total_duration / len(words)

        timings = []
        for i, word in enumerate(words):
            timing = {
                'word': word,
                'start': i * word_duration,
                'end': (i + 1) * word_duration,
                'index': i
            }
            timings.append(timing)

        return timings

    def sync_with_audio_duration(self, text: str, audio_duration: float) -> List[Dict]:
        """Synchronize transcript with actual audio duration"""
        return self.generate_word_timings(text, audio_duration)

    def extract_sentences(self, text: str) -> List[str]:
        """Extract sentences from transcript text"""
        # Simple sentence splitting
        sentences = re.split(r'[.!?]+', text)
        return [s.strip() for s in sentences if s.strip()]
