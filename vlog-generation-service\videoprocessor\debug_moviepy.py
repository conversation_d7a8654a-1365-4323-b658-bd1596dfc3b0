#!/usr/bin/env python3
"""
Debug MoviePy installation and find working imports
"""

print("🔍 MoviePy Debug Script")
print("=" * 50)

# Check basic MoviePy import
try:
    import moviepy
    print(f"✅ MoviePy imported: version {moviepy.__version__}")
    print(f"📁 Location: {moviepy.__file__}")
except ImportError as e:
    print(f"❌ Cannot import moviepy: {e}")
    exit(1)

# Check what's in the main moviepy module
print(f"\n📋 Contents of moviepy module:")
moviepy_contents = [item for item in dir(moviepy) if not item.startswith('_')]
for item in sorted(moviepy_contents):
    print(f"  - {item}")

# Check for submodules
print(f"\n🔍 Checking submodules:")
submodules = ['video', 'audio', 'editor']
for submodule in submodules:
    try:
        exec(f"import moviepy.{submodule}")
        print(f"✅ moviepy.{submodule} available")
    except ImportError as e:
        print(f"❌ moviepy.{submodule} not available: {e}")

# Try specific imports that we need
print(f"\n🎯 Testing specific imports:")

imports_to_test = [
    # MoviePy 1.x style
    "from moviepy.editor import VideoFileClip",
    "from moviepy.editor import ImageClip", 
    "from moviepy.editor import AudioFileClip",
    "from moviepy.editor import ColorClip",
    "from moviepy.editor import CompositeVideoClip",
    "from moviepy.editor import concatenate_videoclips",
    
    # MoviePy 2.x style
    "from moviepy.video.io.VideoFileClip import VideoFileClip",
    "from moviepy.video.VideoClip import ImageClip",
    "from moviepy.video.VideoClip import ColorClip", 
    "from moviepy.audio.io.AudioFileClip import AudioFileClip",
    "from moviepy.video.compositing.CompositeVideoClip import CompositeVideoClip",
    "from moviepy.video.compositing.concatenate import concatenate_videoclips",
    
    # Alternative attempts
    "from moviepy import VideoFileClip",
    "import moviepy.video.io.VideoFileClip",
    "import moviepy.video.VideoClip",
    "import moviepy.audio.io.AudioFileClip",
]

working_imports = []
for import_stmt in imports_to_test:
    try:
        exec(import_stmt)
        print(f"✅ {import_stmt}")
        working_imports.append(import_stmt)
    except ImportError as e:
        print(f"❌ {import_stmt}")
        print(f"   Error: {e}")

# Show working imports
if working_imports:
    print(f"\n🎉 Working imports found:")
    for imp in working_imports:
        print(f"  {imp}")
else:
    print(f"\n⚠️ No working imports found!")

# Try to explore the video module structure
print(f"\n🔍 Exploring moviepy.video structure:")
try:
    import moviepy.video
    video_contents = [item for item in dir(moviepy.video) if not item.startswith('_')]
    print(f"Contents of moviepy.video:")
    for item in sorted(video_contents):
        print(f"  - {item}")
        
    # Check io submodule
    try:
        import moviepy.video.io
        io_contents = [item for item in dir(moviepy.video.io) if not item.startswith('_')]
        print(f"\nContents of moviepy.video.io:")
        for item in sorted(io_contents):
            print(f"  - {item}")
    except ImportError:
        print(f"❌ moviepy.video.io not available")
        
except ImportError:
    print(f"❌ moviepy.video not available")

print(f"\n" + "=" * 50)
print("🏁 Debug complete!")
